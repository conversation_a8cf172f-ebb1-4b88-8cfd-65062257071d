local L0_1, L1_1, L2_1
L0_1 = require
L1_1 = "share"
L0_1(L1_1)
L0_1 = init
L1_1 = 1
L0_1(L1_1)
__isnlog__ = false
UI_API_Key = "bPDOP4AkqUguZpyrAgtmTm0q"
UI_Secret_Key = "P4QdxoSBkw4K267BrgF2Sf76jY5SrM3d"
_ENV["提示字体大小"] = 8
_ENV["提示颜色"] = "0xffffff"
_ENV["字体颜色"] = "0x030303"

function L0_1()
  local L0_2, L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2
  L0_2 = getScreenSize
  L0_2, L1_2 = L0_2()
  h = L1_2
  w = L0_2
  L0_2 = UINew
  L1_2 = {}
  L1_2.titles = "只因你太美"
  L1_2.okname = "Runing(うんてん)"
  L1_2.cancelname = "Quit(しゅうりょう)"
  L1_2.config = "budian.dat"
  L1_2.timer = 80
  L2_2 = w
  L2_2 = L2_2 * 1.75
  L1_2.width = L2_2
  L1_2.pagination = 1
  L1_2.pagenumtype = "number"
  L1_2.selpage = "2"
  L1_2.titlesize = 14
  L0_2(L1_2)
  L0_2 = UILine
  L1_2 = "center"
  L2_2 = 1136
  L3_2 = 5
  L4_2 = "255,0,0"
  L0_2(L1_2, L2_2, L3_2, L4_2)
  L0_2 = UILabel
  L1_2 = "2025抓鬼"
  L2_2 = 15
  L3_2 = "center"
  L4_2 = "160,32,240"
  L5_2 = -1
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2)
  L0_2 = UILabel
  L1_2 = "(必填)"
  L2_2 = 10
  L3_2 = "left"
  L4_2 = "160,32,240"
  L5_2 = 80
  L6_2 = 1
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
  L0_2 = UIRadio
  L1_2 = "rdo"
  L2_2 = "小鬼,大小鬼"
  L3_2 = "1"
  L4_2 = -1
  L5_2 = 0
  L6_2 = ""
  L7_2 = 1
  L8_2 = 6
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  L0_2 = UILabel
  L1_2 = 1
  L2_2 = "长安旗："
  L3_2 = 10
  L4_2 = "left"
  L5_2 = "255,0,0"
  L6_2 = 80
  L7_2 = 1
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  L0_2 = UICombo
  L1_2 = 1
  L2_2 = "UI_qizi_长安"
  L3_2 = "红旗,白旗,黄旗,绿旗,蓝旗"
  L4_2 = "0"
  L5_2 = 160
  L6_2 = 1
  L7_2 = false
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  L0_2 = UILabel
  L1_2 = 1
  L2_2 = "朱紫旗："
  L3_2 = 10
  L4_2 = "left"
  L5_2 = "255,0,0"
  L6_2 = 80
  L7_2 = 1
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  L0_2 = UICombo
  L1_2 = 1
  L2_2 = "UI_qizi_朱紫"
  L3_2 = "白旗,红旗,黄旗,绿旗,蓝旗"
  L4_2 = "0"
  L5_2 = 160
  L6_2 = 0
  L7_2 = false
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  L0_2 = UILabel
  L1_2 = 1
  L2_2 = "傲来旗："
  L3_2 = 10
  L4_2 = "left"
  L5_2 = "255,0,0"
  L6_2 = 80
  L7_2 = 1
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  L0_2 = UICombo
  L1_2 = 1
  L2_2 = "UI_qizi_傲来"
  L3_2 = "黄旗,红旗,白旗,绿旗,蓝旗"
  L4_2 = "0"
  L5_2 = 160
  L6_2 = 1
  L7_2 = false
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  L0_2 = UILabel
  L1_2 = 1
  L2_2 = "长寿旗："
  L3_2 = 10
  L4_2 = "left"
  L5_2 = "255,0,0"
  L6_2 = 80
  L7_2 = 1
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  L0_2 = UICombo
  L1_2 = 1
  L2_2 = "UI_qizi_长寿"
  L3_2 = "绿旗,红旗,白旗,黄旗,蓝旗"
  L4_2 = "0"
  L5_2 = 160
  L6_2 = 0
  L7_2 = false
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  L0_2 = UILabel
  L1_2 = 1
  L2_2 = "低于设置的百分比执行回复"
  L3_2 = 10
  L4_2 = "center"
  L5_2 = "255,0,0"
  L6_2 = -1
  L7_2 = 0
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  L0_2 = UILabel
  L1_2 = 1
  L2_2 = "角色回复"
  L3_2 = 10
  L4_2 = "left"
  L5_2 = "0,0,255"
  L6_2 = 124
  L7_2 = 1
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  L0_2 = UICombo
  L1_2 = 1
  L2_2 = "UI_贼王角色回复"
  L3_2 = "30%,50%,70%,90%"
  L4_2 = "3"
  L5_2 = 124
  L6_2 = 1
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
  L0_2 = UILabel
  L1_2 = 1
  L2_2 = "宠物回复"
  L3_2 = 10
  L4_2 = "left"
  L5_2 = "0,0,255"
  L6_2 = 124
  L7_2 = 1
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  L0_2 = UICombo
  L1_2 = 1
  L2_2 = "UI_贼王宠物回复"
  L3_2 = "30%,50%,70%,90%"
  L4_2 = "3"
  L5_2 = 124
  L6_2 = 0
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
  L0_2 = UILabel
  L1_2 = "若队长为无底洞请打开下列开关"
  L2_2 = 10
  L3_2 = "center"
  L4_2 = "160,32,240"
  L5_2 = -1
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2)
  L0_2 = UILabel
  L1_2 = "战斗加血"
  L2_2 = 10
  L3_2 = "left"
  L4_2 = "255,105,180"
  L5_2 = 80
  L6_2 = 1
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
  L0_2 = UISwitch
  L1_2 = "switch1"
  L2_2 = "off"
  L3_2 = "m"
  L4_2 = "left"
  L0_2(L1_2, L2_2, L3_2, L4_2)
  L0_2 = UILabel
  L1_2 = "延迟"
  L2_2 = 10
  L3_2 = "left"
  L4_2 = "0,0,255"
  L5_2 = 80
  L6_2 = 1
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2)
  L0_2 = UIEdit
  L1_2 = "延迟"
  L2_2 = "延迟"
  L3_2 = "0"
  L4_2 = 10
  L5_2 = "left"
  L6_2 = "255,0,0"
  L7_2 = "default"
  L8_2 = w
  L8_2 = L8_2 * 0.25
  L9_2 = 1
  L0_2(L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2)
  L0_2 = UIShow
  L0_2()
end

_UI = L0_1
L0_1 = _UI
L0_1()
L0_1 = rdo
if L0_1 == "小鬼" then
  L0_1 = _ENV["__抓鬼"]
  L0_1 = L0_1.main
  L0_1()
else
  L0_1 = rdo
  if L0_1 == "大小鬼" then
    L0_1 = _ENV["__抓鬼"]
    L0_1 = L0_1["大小鬼流程"]
    L0_1()
  end
end
