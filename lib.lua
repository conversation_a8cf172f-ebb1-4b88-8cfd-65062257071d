-- 导入必要的库文件
require("TSLib")
ts = require("ts")
json = require("luajson")

-- 初始化
init(1)

-- 设置日志标识
local logTag = "hblog"

-- 初始化随机数种子
math.randomseed(getRndNum())

-- 创建必要的目录
local userPathStr = userPath()
os.execute("mkdir " .. userPathStr .. "/res/360ex")
os.execute("mkdir " .. userPathStr .. "/res/hbbug")

-- 查找文字并点击的函数
function findTextAndClick(text, x1, y1, x2, y2, color, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 85
  end

  -- 保存当前坐标
  local savedX = pyx
  local savedY = pyy or 0
  pyy = savedY
  pyx = savedX

  -- 查找文字
  local foundX, foundY = tsFindText(text, x1, y1, x2, y2, color, similarity)

  -- 如果找到文字则点击
  if foundX > -1 then
    Click(foundX, foundY, 0)
    return true
  end

  return false
end

-- 为了保持兼容性，保留原函数名
Fw_click = findTextAndClick

-- OCR文字识别并返回布尔值的函数
function findWordReturnBool(region, expectedText, x1, y1, x2, y2, color, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 85
  end

  -- 使用OCR识别文字
  local recognizedText = tsOcrText(region, x1, y1, x2, y2, color, similarity)

  -- 比较识别的文字是否与期望文字相同
  if recognizedText == expectedText then
    return true
  end

  return false
end

-- 为了保持兼容性，保留原函数名
__Findwordrebool = findWordReturnBool

-- 多色查找并点击的函数
function myFindColorAndClick(colorArray, index)
  -- 从颜色数组中获取指定索引的颜色信息
  local colorInfo = colorArray[index]

  -- 执行多色查找
  local foundX, foundY = findMultiColorInRegionFuzzy(
    colorInfo[1], -- 第一个颜色
    colorInfo[2], -- 第二个颜色
    colorInfo[3], -- 第三个颜色
    colorInfo[4], -- 第四个颜色
    colorInfo[5], -- 第五个颜色
    colorInfo[6], -- 第六个颜色
    colorInfo[7]  -- 第七个颜色
  )

  -- 保存找到的坐标到全局变量
  y = foundY
  x = foundX

  -- 如果找到则点击
  if foundX ~= -1 then
    Click(foundX, foundY)
    return true
  end

  return false
end

-- 为了保持兼容性，保留原函数名
My_tfc = myFindColorAndClick

-- 颜色检测函数
function colorCheck(colorData, shouldClick, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 90
  end

  -- 检测多色
  local colorFound = multiColor(colorData[2], similarity)

  if colorFound then
    -- 如果需要点击且参数允许点击
    if shouldClick == nil or shouldClick == true then
      local clickX = colorData[2][1][1]
      local clickY = colorData[2][1][2]
      Click(clickX, clickY)
    end
    return true
  end

  return false
end

-- 为了保持兼容性，保留原函数名
CC = colorCheck

-- 字符串分割函数
function stringSplit(inputString, delimiter)
  local result = {}
  local pattern = "[^" .. delimiter .. "]+"

  -- 使用gsub函数分割字符串
  string.gsub(inputString, pattern, function(match)
    table.insert(result, match)
  end)

  return result
end

-- 为了保持兼容性，保留原函数名
my_Split = stringSplit

-- 生成随机数的函数
function randomNumber(min, max)
  return math.random(min, max)
end

-- 为了保持兼容性，保留原函数名
Rnumber = randomNumber

-- 随机睡眠函数
function randomSleep(minTime, maxTime)
  local sleepTime = math.random(minTime, maxTime)
  mSleep(sleepTime)
end

-- 为了保持兼容性，保留原函数名
_Sleep = randomSleep

-- 日志打印函数
function printLog(message)
  -- 如果启用了日志且消息不为空
  if __isnlog__ and message ~= "" then
    nLog(message)
    log(message, logTag)
  end
end

-- 为了保持兼容性，保留原函数名
_print = printLog

-- 模拟触摸点击函数（支持单击和双击）
function simulateTap(x, y, tapType, randomOffset)
  -- 设置默认随机偏移量
  if not randomOffset then
    randomOffset = 5
  end

  -- 添加随机偏移以模拟真实点击
  local offsetX = math.random(-randomOffset, randomOffset)
  local offsetY = math.random(-randomOffset, randomOffset)
  x = x + offsetX
  y = y + offsetY

  if tapType == 2 then
    -- 双击模式
    -- 第一次点击
    touchDown(1, x, y)
    mSleep(math.random(50, 200))
    touchUp(1, x, y)
    mSleep(math.random(50, 150))

    -- 为第二次点击添加新的随机偏移
    offsetX = math.random(-randomOffset, randomOffset)
    offsetY = math.random(-randomOffset, randomOffset)
    x = x + offsetX
    y = y + offsetY

    -- 第二次点击
    touchDown(1, x, y)
    mSleep(math.random(30, 200))
    touchUp(1, x, y)
  else
    -- 单击模式
    touchDown(1, x, y)
    mSleep(math.random(50, 300))
    touchUp(1, x, y)
  end
end

-- 为了保持兼容性，保留原函数名
_tap = simulateTap

-- 多色查找并双击的函数
function findColorAndDoubleClick(colorData, x1, y1, x2, y2, randomOffset, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 90
  end
  -- 设置默认随机偏移
  if not randomOffset then
    randomOffset = 5
  end

  -- 在指定区域查找多色
  local foundX, foundY = findMultiColorInRegionFuzzyByTable(
    colorData[2], similarity, x1, y1, x2, y2
  )

  -- 如果找到颜色则执行双击
  if foundX ~= -1 then
    DClick(foundX, foundY, randomOffset)
    return true
  else
    return false
  end
end

-- 为了保持兼容性，保留原函数名
FC_DClicke = findColorAndDoubleClick

-- 双击函数（带随机偏移）
function doubleClick(x, y, randomOffset)
  if randomOffset == nil then
    -- 使用默认偏移量5
    -- 第一次点击
    local offsetX = Rnumber(-5, 5)
    local offsetY = Rnumber(-5, 5)
    x = x + offsetX
    y = y + offsetY

    touchDown(1, x, y)
    mSleep(Rnumber(30, 100))
    touchUp(1, x, y)

    -- 第二次点击（重新计算偏移）
    offsetX = Rnumber(-5, 5)
    offsetY = Rnumber(-5, 5)
    x = x + offsetX
    y = y + offsetY

    touchDown(1, x, y)
    mSleep(Rnumber(30, 100))
    touchUp(1, x, y)
  else
    -- 使用自定义偏移量
    -- 第一次点击
    local offsetX = Rnumber(-randomOffset, randomOffset)
    local offsetY = Rnumber(-randomOffset, randomOffset)
    x = x + offsetX
    y = y + offsetY

    touchDown(1, x, y)
    mSleep(Rnumber(30, 100))
    touchUp(1, x, y)

    -- 第二次点击（重新计算偏移）
    offsetX = Rnumber(-randomOffset, randomOffset)
    offsetY = Rnumber(-randomOffset, randomOffset)
    x = x + offsetX
    y = y + offsetY

    touchDown(1, x, y)
    mSleep(Rnumber(30, 100))
    touchUp(1, x, y)
  end
end

-- 为了保持兼容性，保留原函数名
DClick = doubleClick

-- 多色查找并双击函数（使用区域参数）
function findColorAndDoubleClickInRegion(colorData, randomOffset, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 90
  end
  -- 设置默认随机偏移
  if not randomOffset then
    randomOffset = 5
  end

  -- 从颜色数据中提取区域参数
  local regionData = colorData[1]
  local x1 = regionData[2]
  local y1 = regionData[3]
  local x2 = regionData[4]
  local y2 = regionData[5]

  -- 在指定区域查找多色
  local foundX, foundY = findMultiColorInRegionFuzzyByTable(
    colorData[2], similarity, x1, y1, x2, y2
  )

  -- 如果找到颜色则执行双击
  if foundX ~= -1 then
    DClick(foundX, foundY, randomOffset)
    return true
  else
    return false
  end
end

-- 为了保持兼容性，保留原函数名
FC_DClick = findColorAndDoubleClickInRegion

-- 颜色比较并点击函数
function compareColorAndClick(colorData, similarity)
  -- 设置默认相似度
  if not similarity then
    similarity = 90
  end

  -- 检测多色
  local colorFound = multiColor(colorData[2], similarity)

  if colorFound then
    -- 检查是否需要点击
    local shouldClick = colorData[1][2]
    if shouldClick then
      local clickX = colorData[2][1][1]
      local clickY = colorData[2][1][2]
      _tap(clickX, clickY)
    end

    -- 打印日志
    local logMessage = colorData[1][1]
    _print(logMessage)
    return true
  end

  return false
end

-- 为了保持兼容性，保留原函数名
_cmp = compareColorAndClick

function L1_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2
  if not A1_2 then
    A1_2 = 0
  end
  if not A2_2 then
    A2_2 = 0
  end
  L3_2 = multiColor
  L4_2 = A0_2[2]
  L5_2 = 90
  L3_2 = L3_2(L4_2, L5_2)
  if L3_2 then
    L3_2 = A0_2[1]
    L3_2 = L3_2[2]
    if L3_2 then
      L3_2 = _tap
      L4_2 = A0_2[2]
      L4_2 = L4_2[1]
      L4_2 = L4_2[1]
      L4_2 = L4_2 + A1_2
      L5_2 = A0_2[2]
      L5_2 = L5_2[1]
      L5_2 = L5_2[2]
      L5_2 = L5_2 + A2_2
      L3_2(L4_2, L5_2)
    end
    L3_2 = _print
    L4_2 = A0_2[1]
    L4_2 = L4_2[1]
    L3_2(L4_2)
    L3_2 = true
    return L3_2
  end
  L3_2 = false
  return L3_2
end

_cmp_p = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L2_2 = 1
  L3_2 = A1_2[1]
  L4_2 = 1
  for L5_2 = L2_2, L3_2, L4_2 do
    L6_2 = multiColor
    L7_2 = A0_2[2]
    L8_2 = 90
    L6_2 = L6_2(L7_2, L8_2)
    if L6_2 then
      L6_2 = A0_2[1]
      L6_2 = L6_2[2]
      if L6_2 then
        L6_2 = _tap
        L7_2 = A0_2[2]
        L7_2 = L7_2[1]
        L7_2 = L7_2[1]
        L8_2 = A0_2[2]
        L8_2 = L8_2[1]
        L8_2 = L8_2[2]
        L6_2(L7_2, L8_2)
      end
      L6_2 = _print
      L7_2 = A0_2[1]
      L7_2 = L7_2[1]
      L6_2(L7_2)
      L6_2 = true
      return L6_2
    end
    L6_2 = mSleep
    L7_2 = A1_2[2]
    L6_2(L7_2)
  end
  L2_2 = false
  return L2_2
end

_cmp_cx = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  if not A1_2 then
    A1_2 = 90
  end
  if not A2_2 then
    A2_2 = 0
  end
  if not A3_2 then
    A3_2 = 0
  end
  L4_2 = keepScreen
  L5_2 = true
  L4_2(L5_2)
  L4_2 = pairs
  L5_2 = A0_2
  L4_2, L5_2, L6_2 = L4_2(L5_2)
  for L7_2, L8_2 in L4_2, L5_2, L6_2 do
    L9_2 = multiColor
    L10_2 = L8_2[2]
    L11_2 = A1_2
    L12_2 = false
    L9_2 = L9_2(L10_2, L11_2, L12_2)
    if L9_2 then
      L9_2 = L8_2[1]
      L9_2 = L9_2[2]
      if L9_2 then
        L9_2 = _tap
        L10_2 = L8_2[2]
        L10_2 = L10_2[1]
        L10_2 = L10_2[1]
        L11_2 = L8_2[2]
        L11_2 = L11_2[1]
        L11_2 = L11_2[2]
        L9_2(L10_2, L11_2)
      end
      L9_2 = _print
      L10_2 = L8_2[1]
      L10_2 = L10_2[1]
      L9_2(L10_2)
      L9_2 = keepScreen
      L10_2 = false
      L9_2(L10_2)
      L9_2 = true
      return L9_2
    end
  end
  L4_2 = keepScreen
  L5_2 = false
  L4_2(L5_2)
  L4_2 = false
  return L4_2
end

_cmp_tb = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2, A4_2)
  local L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  if not A2_2 then
    A2_2 = 90
  end
  if not A3_2 then
    A3_2 = 0
  end
  if not A4_2 then
    A4_2 = 0
  end
  L5_2 = 1
  L6_2 = A1_2[1]
  L7_2 = 1
  for L8_2 = L5_2, L6_2, L7_2 do
    L9_2 = keepScreen
    L10_2 = true
    L9_2(L10_2)
    L9_2 = pairs
    L10_2 = A0_2
    L9_2, L10_2, L11_2 = L9_2(L10_2)
    for L12_2, L13_2 in L9_2, L10_2, L11_2 do
      L14_2 = multiColor
      L15_2 = L13_2[2]
      L16_2 = A2_2
      L17_2 = false
      L14_2 = L14_2(L15_2, L16_2, L17_2)
      if L14_2 then
        L14_2 = L13_2[1]
        L14_2 = L14_2[2]
        if L14_2 then
          L14_2 = _tap
          L15_2 = L13_2[2]
          L15_2 = L15_2[1]
          L15_2 = L15_2[1]
          L16_2 = L13_2[2]
          L16_2 = L16_2[1]
          L16_2 = L16_2[2]
          L14_2(L15_2, L16_2)
        end
        L14_2 = _print
        L15_2 = L13_2[1]
        L15_2 = L15_2[1]
        L14_2(L15_2)
        L14_2 = keepScreen
        L15_2 = false
        L14_2(L15_2)
        L14_2 = true
        return L14_2
      end
    end
    L9_2 = keepScreen
    L10_2 = false
    L9_2(L10_2)
    L9_2 = _Sleep
    L10_2 = 20
    L11_2 = 150
    L9_2(L10_2, L11_2)
  end
  L5_2 = false
  return L5_2
end

_cmp_tb_cx = L1_1

function L1_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  if not A1_2 then
    L3_2 = {}
    L4_2 = 0
    L5_2 = 0
    L3_2[1] = L4_2
    L3_2[2] = L5_2
    A1_2 = L3_2
  end
  L3_2 = false
  L4_2 = -1
  L5_2 = -1
  if A2_2 ~= nil then
    L6_2 = findMultiColorInRegionFuzzy
    L7_2 = A0_2[3]
    L7_2 = L7_2[1]
    L8_2 = A0_2[3]
    L8_2 = L8_2[2]
    L9_2 = A0_2[2]
    L9_2 = L9_2[1]
    L10_2 = A2_2[1]
    L11_2 = A2_2[2]
    L12_2 = A2_2[3]
    L13_2 = A2_2[4]
    L6_2, L7_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
    L5_2 = L7_2
    L4_2 = L6_2
  else
    L6_2 = findMultiColorInRegionFuzzy
    L7_2 = A0_2[3]
    L7_2 = L7_2[1]
    L8_2 = A0_2[3]
    L8_2 = L8_2[2]
    L9_2 = A0_2[2]
    L9_2 = L9_2[1]
    L10_2 = A0_2[1]
    L10_2 = L10_2[2]
    L11_2 = A0_2[1]
    L11_2 = L11_2[3]
    L12_2 = A0_2[1]
    L12_2 = L12_2[4]
    L13_2 = A0_2[1]
    L13_2 = L13_2[5]
    L6_2, L7_2 = L6_2(L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
    L5_2 = L7_2
    L4_2 = L6_2
  end
  if -1 < L4_2 then
    L6_2 = A0_2[2]
    L6_2 = L6_2[2]
    if L6_2 then
      L6_2 = A0_2[2]
      L6_2 = L6_2[3]
      if L6_2 == 1 then
        L6_2 = _tap
        L7_2 = A1_2[1]
        L7_2 = L4_2 + L7_2
        L8_2 = A1_2[2]
        L8_2 = L5_2 + L8_2
        L6_2(L7_2, L8_2)
      else
        L6_2 = _tap
        L7_2 = A1_2[1]
        L7_2 = L4_2 + L7_2
        L8_2 = A1_2[2]
        L8_2 = L5_2 + L8_2
        L9_2 = 2
        L6_2(L7_2, L8_2, L9_2)
      end
    end
    L6_2 = _print
    L7_2 = A0_2[1]
    L7_2 = L7_2[1]
    L6_2(L7_2)
    L3_2 = true
  end
  L6_2 = L3_2
  L7_2 = L4_2
  L8_2 = L5_2
  return L6_2, L7_2, L8_2
end

_find = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2
  if not A2_2 then
    L4_2 = {}
    L5_2 = 0
    L6_2 = 0
    L4_2[1] = L5_2
    L4_2[2] = L6_2
    A2_2 = L4_2
  end
  L4_2 = false
  L5_2 = -1
  L6_2 = -1
  L6_2 = 1
  L7_2 = A1_2[1]
  L8_2 = 1
  for L9_2 = L6_2, L7_2, L8_2 do
    if A3_2 ~= nil then
      L10_2 = findMultiColorInRegionFuzzy
      L11_2 = A0_2[3]
      L11_2 = L11_2[1]
      L12_2 = A0_2[3]
      L12_2 = L12_2[2]
      L13_2 = A0_2[2]
      L13_2 = L13_2[1]
      L14_2 = A3_2[1]
      L15_2 = A3_2[2]
      L16_2 = A3_2[3]
      L17_2 = A3_2[4]
      L10_2, L11_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
      L5_2 = L11_2
      L4_2 = L10_2
    else
      L10_2 = findMultiColorInRegionFuzzy
      L11_2 = A0_2[3]
      L11_2 = L11_2[1]
      L12_2 = A0_2[3]
      L12_2 = L12_2[2]
      L13_2 = A0_2[2]
      L13_2 = L13_2[1]
      L14_2 = A0_2[1]
      L14_2 = L14_2[2]
      L15_2 = A0_2[1]
      L15_2 = L15_2[3]
      L16_2 = A0_2[1]
      L16_2 = L16_2[4]
      L17_2 = A0_2[1]
      L17_2 = L17_2[5]
      L10_2, L11_2 = L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2)
      L5_2 = L11_2
      L4_2 = L10_2
    end
    if -1 < L4_2 then
      L10_2 = A0_2[2]
      L10_2 = L10_2[2]
      if L10_2 then
        L10_2 = A0_2[2]
        L10_2 = L10_2[3]
        if L10_2 == 1 then
          L10_2 = _tap
          L11_2 = A2_2[1]
          L11_2 = L4_2 + L11_2
          L12_2 = A2_2[2]
          L12_2 = L5_2 + L12_2
          L10_2(L11_2, L12_2)
        else
          L10_2 = _tap
          L11_2 = A2_2[1]
          L11_2 = L4_2 + L11_2
          L12_2 = A2_2[2]
          L12_2 = L5_2 + L12_2
          L13_2 = 2
          L10_2(L11_2, L12_2, L13_2)
        end
      end
      L10_2 = _print
      L11_2 = A0_2[1]
      L11_2 = L11_2[1]
      L10_2(L11_2)
      L10_2 = true
      L11_2 = L4_2
      L12_2 = L5_2
      return L10_2, L11_2, L12_2
    end
    L10_2 = mSleep
    L11_2 = A1_2[2]
    L10_2(L11_2)
  end
  L6_2 = false
  L7_2 = L4_2
  L8_2 = L5_2
  return L6_2, L7_2, L8_2
end

_find_cx = L1_1

function L1_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2
  if not A1_2 then
    L3_2 = {}
    L4_2 = 0
    L5_2 = 0
    L3_2[1] = L4_2
    L3_2[2] = L5_2
    A1_2 = L3_2
  end
  L3_2 = false
  L4_2 = -1
  L5_2 = -1
  L6_2 = keepScreen
  L7_2 = true
  L6_2(L7_2)
  if A2_2 ~= nil then
    L6_2 = pairs
    L7_2 = A0_2
    L6_2, L7_2, L8_2 = L6_2(L7_2)
    for L9_2, L10_2 in L6_2, L7_2, L8_2 do
      L11_2 = findMultiColorInRegionFuzzy
      L12_2 = L10_2[3]
      L12_2 = L12_2[1]
      L13_2 = L10_2[3]
      L13_2 = L13_2[2]
      L14_2 = L10_2[2]
      L14_2 = L14_2[1]
      L15_2 = A2_2[1]
      L16_2 = A2_2[2]
      L17_2 = A2_2[3]
      L18_2 = A2_2[4]
      L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      L5_2 = L12_2
      L4_2 = L11_2
      if -1 < L4_2 then
        L11_2 = L10_2[2]
        L11_2 = L11_2[2]
        if L11_2 then
          L11_2 = L10_2[2]
          L11_2 = L11_2[3]
          if L11_2 == 1 then
            L11_2 = _tap
            L12_2 = A1_2[1]
            L12_2 = L4_2 + L12_2
            L13_2 = A1_2[2]
            L13_2 = L5_2 + L13_2
            L11_2(L12_2, L13_2)
          else
            L11_2 = _tap
            L12_2 = A1_2[1]
            L12_2 = L4_2 + L12_2
            L13_2 = A1_2[2]
            L13_2 = L5_2 + L13_2
            L14_2 = 2
            L11_2(L12_2, L13_2, L14_2)
          end
        end
        L11_2 = _print
        L12_2 = L10_2[1]
        L12_2 = L12_2[1]
        L11_2(L12_2)
        L3_2 = true
        break
      end
    end
  else
    L6_2 = pairs
    L7_2 = A0_2
    L6_2, L7_2, L8_2 = L6_2(L7_2)
    for L9_2, L10_2 in L6_2, L7_2, L8_2 do
      L11_2 = findMultiColorInRegionFuzzy
      L12_2 = L10_2[3]
      L12_2 = L12_2[1]
      L13_2 = L10_2[3]
      L13_2 = L13_2[2]
      L14_2 = L10_2[2]
      L14_2 = L14_2[1]
      L15_2 = L10_2[1]
      L15_2 = L15_2[2]
      L16_2 = L10_2[1]
      L16_2 = L16_2[3]
      L17_2 = L10_2[1]
      L17_2 = L17_2[4]
      L18_2 = L10_2[1]
      L18_2 = L18_2[5]
      L11_2, L12_2 = L11_2(L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2)
      L5_2 = L12_2
      L4_2 = L11_2
      if -1 < L4_2 then
        L11_2 = L10_2[2]
        L11_2 = L11_2[2]
        if L11_2 then
          L11_2 = L10_2[2]
          L11_2 = L11_2[3]
          if L11_2 == 1 then
            L11_2 = _tap
            L12_2 = A1_2[1]
            L12_2 = L4_2 + L12_2
            L13_2 = A1_2[2]
            L13_2 = L5_2 + L13_2
            L11_2(L12_2, L13_2)
          else
            L11_2 = _tap
            L12_2 = A1_2[1]
            L12_2 = L4_2 + L12_2
            L13_2 = A1_2[2]
            L13_2 = L5_2 + L13_2
            L14_2 = 2
            L11_2(L12_2, L13_2, L14_2)
          end
        end
        L11_2 = _print
        L12_2 = L10_2[1]
        L12_2 = L12_2[1]
        L11_2(L12_2)
        L3_2 = true
        break
      end
    end
  end
  L6_2 = keepScreen
  L7_2 = false
  L6_2(L7_2)
  L6_2 = L3_2
  L7_2 = L4_2
  L8_2 = L5_2
  return L6_2, L7_2, L8_2
end

_find_tb = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2
  if not A2_2 then
    L4_2 = {}
    L5_2 = 0
    L6_2 = 0
    L4_2[1] = L5_2
    L4_2[2] = L6_2
    A2_2 = L4_2
  end
  L4_2 = -1
  L5_2 = -1
  if A3_2 ~= nil then
    L6_2 = 1
    L7_2 = A1_2[1]
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = keepScreen
      L11_2 = true
      L10_2(L11_2)
      L10_2 = pairs
      L11_2 = A0_2
      L10_2, L11_2, L12_2 = L10_2(L11_2)
      for L13_2, L14_2 in L10_2, L11_2, L12_2 do
        L15_2 = findMultiColorInRegionFuzzy
        L16_2 = L14_2[3]
        L16_2 = L16_2[1]
        L17_2 = L14_2[3]
        L17_2 = L17_2[2]
        L18_2 = L14_2[2]
        L18_2 = L18_2[1]
        L19_2 = A3_2[1]
        L20_2 = A3_2[2]
        L21_2 = A3_2[3]
        L22_2 = A3_2[4]
        L15_2, L16_2 = L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
        L5_2 = L16_2
        L4_2 = L15_2
        if -1 < L4_2 then
          L15_2 = L14_2[2]
          L15_2 = L15_2[2]
          if L15_2 then
            L15_2 = L14_2[2]
            L15_2 = L15_2[3]
            if L15_2 == 1 then
              L15_2 = _tap
              L16_2 = A2_2[1]
              L16_2 = L4_2 + L16_2
              L17_2 = A2_2[2]
              L17_2 = L5_2 + L17_2
              L15_2(L16_2, L17_2)
            else
              L15_2 = _tap
              L16_2 = A2_2[1]
              L16_2 = L4_2 + L16_2
              L17_2 = A2_2[2]
              L17_2 = L5_2 + L17_2
              L18_2 = 2
              L15_2(L16_2, L17_2, L18_2)
            end
          end
          L15_2 = _print
          L16_2 = L14_2[1]
          L16_2 = L16_2[1]
          L15_2(L16_2)
          L15_2 = keepScreen
          L16_2 = false
          L15_2(L16_2)
          L15_2 = true
          L16_2 = L4_2
          L17_2 = L5_2
          return L15_2, L16_2, L17_2
        end
      end
      L10_2 = keepScreen
      L11_2 = false
      L10_2(L11_2)
      L10_2 = mSleep
      L11_2 = A1_2[2]
      L10_2(L11_2)
    end
  else
    L6_2 = 1
    L7_2 = A1_2[1]
    L8_2 = 1
    for L9_2 = L6_2, L7_2, L8_2 do
      L10_2 = keepScreen
      L11_2 = true
      L10_2(L11_2)
      L10_2 = pairs
      L11_2 = A0_2
      L10_2, L11_2, L12_2 = L10_2(L11_2)
      for L13_2, L14_2 in L10_2, L11_2, L12_2 do
        L15_2 = findMultiColorInRegionFuzzy
        L16_2 = L14_2[3]
        L16_2 = L16_2[1]
        L17_2 = L14_2[3]
        L17_2 = L17_2[2]
        L18_2 = L14_2[2]
        L18_2 = L18_2[1]
        L19_2 = L14_2[1]
        L19_2 = L19_2[2]
        L20_2 = L14_2[1]
        L20_2 = L20_2[3]
        L21_2 = L14_2[1]
        L21_2 = L21_2[4]
        L22_2 = L14_2[1]
        L22_2 = L22_2[5]
        L15_2, L16_2 = L15_2(L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
        L5_2 = L16_2
        L4_2 = L15_2
        if -1 < L4_2 then
          L15_2 = L14_2[2]
          L15_2 = L15_2[2]
          if L15_2 then
            L15_2 = L14_2[2]
            L15_2 = L15_2[3]
            if L15_2 == 1 then
              L15_2 = _tap
              L16_2 = A2_2[1]
              L16_2 = L4_2 + L16_2
              L17_2 = A2_2[2]
              L17_2 = L5_2 + L17_2
              L15_2(L16_2, L17_2)
            else
              L15_2 = _tap
              L16_2 = A2_2[1]
              L16_2 = L4_2 + L16_2
              L17_2 = A2_2[2]
              L17_2 = L5_2 + L17_2
              L18_2 = 2
              L15_2(L16_2, L17_2, L18_2)
            end
          end
          L15_2 = _print
          L16_2 = L14_2[1]
          L16_2 = L16_2[1]
          L15_2(L16_2)
          L15_2 = keepScreen
          L16_2 = false
          L15_2(L16_2)
          L15_2 = true
          L16_2 = L4_2
          L17_2 = L5_2
          return L15_2, L16_2, L17_2
        end
      end
      L10_2 = keepScreen
      L11_2 = false
      L10_2(L11_2)
      L10_2 = mSleep
      L11_2 = A1_2[2]
      L10_2(L11_2)
    end
  end
  L6_2 = keepScreen
  L7_2 = false
  L6_2(L7_2)
  L6_2 = false
  L7_2 = -1
  L8_2 = -1
  return L6_2, L7_2, L8_2
end

_find_tb_cx = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2
  if not A2_2 then
    L4_2 = {}
    L5_2 = 1
    L6_2 = 0
    L4_2[1] = L5_2
    L4_2[2] = L6_2
    A2_2 = L4_2
  end
  if not A3_2 then
    A3_2 = 1
  end
  L4_2 = {}
  L5_2 = 1
  L6_2 = A2_2[1]
  L7_2 = 1
  for L8_2 = L5_2, L6_2, L7_2 do
    L9_2 = keepScreen
    L10_2 = true
    L9_2(L10_2)
    L9_2 = pairs
    L10_2 = A0_2
    L9_2, L10_2, L11_2 = L9_2(L10_2)
    for L12_2, L13_2 in L9_2, L10_2, L11_2 do
      L14_2 = findMultiColorInRegionFuzzyExt
      L15_2 = L13_2[3]
      L15_2 = L15_2[1]
      L16_2 = L13_2[3]
      L16_2 = L16_2[2]
      L17_2 = L13_2[2]
      L17_2 = L17_2[1]
      L18_2 = A1_2[1]
      L19_2 = A1_2[2]
      L20_2 = A1_2[3]
      L21_2 = A1_2[4]
      L22_2 = {}
      L22_2.orient = A3_2
      L14_2 = L14_2(L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2)
      L15_2 = #L14_2
      if L15_2 ~= 0 then
        L15_2 = _print
        L16_2 = L13_2[1]
        L16_2 = L16_2[1]
        L15_2(L16_2)
        L15_2 = pairs
        L16_2 = L14_2
        L15_2, L16_2, L17_2 = L15_2(L16_2)
        for L18_2, L19_2 in L15_2, L16_2, L17_2 do
          L20_2 = #L4_2
          L20_2 = L20_2 + 1
          L4_2[L20_2] = L19_2
        end
      end
    end
    L9_2 = #L4_2
    if L9_2 ~= 0 then
      L9_2 = keepScreen
      L10_2 = false
      L9_2(L10_2)
      break
    end
    L9_2 = mSleep
    L10_2 = A2_2[2]
    L9_2(L10_2)
    L9_2 = keepScreen
    L10_2 = false
    L9_2(L10_2)
  end
  L5_2 = #L4_2
  if L5_2 ~= 0 then
    L5_2 = 1
    L6_2 = #L4_2
    L6_2 = L6_2 - 1
    L7_2 = 1
    for L8_2 = L5_2, L6_2, L7_2 do
      L9_2 = L8_2 + 1
      L10_2 = #L4_2
      L11_2 = 1
      for L12_2 = L9_2, L10_2, L11_2 do
        L13_2 = L4_2[L8_2]
        L13_2 = L13_2.y
        L14_2 = L4_2[L12_2]
        L14_2 = L14_2.y
        L13_2 = L13_2 - L14_2
        if L13_2 < 5 and -5 < L13_2 then
          L14_2 = L4_2[L12_2]
          L14_2.y = -1
        end
      end
    end
    L5_2 = #L4_2
    L6_2 = 1
    L7_2 = -1
    for L8_2 = L5_2, L6_2, L7_2 do
      L9_2 = L4_2[L8_2]
      L9_2 = L9_2.y
      if L9_2 == -1 then
        L9_2 = table
        L9_2 = L9_2.remove
        L10_2 = L4_2
        L11_2 = L8_2
        L9_2(L10_2, L11_2)
      end
    end
  end
  return L4_2
end

_find_ex = L1_1

function L1_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2
  L1_2 = {}
  L2_2 = 1
  while true do
    L3_2 = #A0_2
    if L2_2 > L3_2 then
      break
    end
    L3_2 = string
    L3_2 = L3_2.byte
    L4_2 = A0_2
    L5_2 = L2_2
    L3_2 = L3_2(L4_2, L5_2)
    if not L3_2 then
      break
    end
    if L3_2 < 192 then
      if 48 <= L3_2 and L3_2 <= 57 or 65 <= L3_2 and L3_2 <= 90 or 97 <= L3_2 and L3_2 <= 122 then
        L4_2 = table
        L4_2 = L4_2.insert
        L5_2 = L1_2
        L6_2 = string
        L6_2 = L6_2.char
        L7_2 = L3_2
        L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2 = L6_2(L7_2)
        L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
      end
      L2_2 = L2_2 + 1
    elseif L3_2 < 224 then
      L2_2 = L2_2 + 2
    elseif L3_2 < 240 then
      if 228 <= L3_2 and L3_2 <= 233 then
        L4_2 = string
        L4_2 = L4_2.byte
        L5_2 = A0_2
        L6_2 = L2_2 + 1
        L4_2 = L4_2(L5_2, L6_2)
        L5_2 = string
        L5_2 = L5_2.byte
        L6_2 = A0_2
        L7_2 = L2_2 + 2
        L5_2 = L5_2(L6_2, L7_2)
        if L4_2 and L5_2 then
          L6_2 = 128
          L7_2 = 191
          L8_2 = 128
          L9_2 = 191
          if L3_2 == 228 then
            L6_2 = 184
          elseif L3_2 == 233 then
            L10_2 = 190
            if L4_2 ~= 190 then
              L11_2 = 191
              if L11_2 then
                goto lbl_78
                L9_2 = L11_2 or L9_2
              end
            end
            L9_2 = 165
            ::lbl_78::
            L7_2 = L10_2
          end
          if L4_2 >= L6_2 and L4_2 <= L7_2 and L5_2 >= L8_2 and L5_2 <= L9_2 then
            L10_2 = table
            L10_2 = L10_2.insert
            L11_2 = L1_2
            L12_2 = string
            L12_2 = L12_2.char
            L13_2 = L3_2
            L14_2 = L4_2
            L15_2 = L5_2
            L12_2, L13_2, L14_2, L15_2, L16_2 = L12_2(L13_2, L14_2, L15_2)
            L10_2(L11_2, L12_2, L13_2, L14_2, L15_2, L16_2)
          end
        end
      end
      L2_2 = L2_2 + 3
    elseif L3_2 < 248 then
      L2_2 = L2_2 + 4
    elseif L3_2 < 252 then
      L2_2 = L2_2 + 5
    elseif L3_2 < 254 then
      L2_2 = L2_2 + 6
    end
  end
  L3_2 = table
  L3_2 = L3_2.concat
  L4_2 = L1_2
  return L3_2(L4_2)
end

_ENV["_过滤特殊字符"] = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2)
  local L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L7_2 = findColorInRegionFuzzy
  L8_2 = A0_2
  L9_2 = A1_2
  L10_2 = A2_2
  L11_2 = A3_2
  L12_2 = A4_2
  L13_2 = A5_2
  L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2)
  if -1 < L7_2 then
    if A6_2 then
      L9_2 = _tap
      L10_2 = L7_2
      L11_2 = L8_2
      L9_2(L10_2, L11_2)
    end
    L9_2 = true
    return L9_2
  else
    L9_2 = false
    return L9_2
  end
end

_ENV["_单点找色"] = L1_1

function L1_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2, L17_2, L18_2, L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2, L54_2, L55_2, L56_2, L57_2, L58_2, L59_2
  L1_2 = io
  L1_2 = L1_2.open
  L2_2 = A0_2
  L3_2 = "rb"
  L1_2 = L1_2(L2_2, L3_2)
  L3_2 = L1_2
  L2_2 = L1_2.read
  L4_2 = "*all"
  L2_2 = L2_2(L3_2, L4_2)
  L4_2 = L1_2
  L3_2 = L1_2.close
  L3_2(L4_2)
  L3_2 = L2_2
  L4_2 = bit32
  L4_2 = L4_2.bor
  L5_2 = bit32
  L5_2 = L5_2.band
  L6_2 = bit32
  L6_2 = L6_2.lshift
  L7_2 = bit32
  L7_2 = L7_2.rshift
  L8_2 = {}
  L9_2 = "A"
  L10_2 = "B"
  L11_2 = "C"
  L12_2 = "D"
  L13_2 = "E"
  L14_2 = "F"
  L15_2 = "G"
  L16_2 = "H"
  L17_2 = "I"
  L18_2 = "J"
  L19_2 = "K"
  L20_2 = "L"
  L21_2 = "M"
  L22_2 = "N"
  L23_2 = "O"
  L24_2 = "P"
  L25_2 = "Q"
  L26_2 = "R"
  L27_2 = "S"
  L28_2 = "T"
  L29_2 = "U"
  L30_2 = "V"
  L31_2 = "W"
  L32_2 = "X"
  L33_2 = "Y"
  L34_2 = "Z"
  L35_2 = "a"
  L36_2 = "b"
  L37_2 = "c"
  L38_2 = "d"
  L39_2 = "e"
  L40_2 = "f"
  L41_2 = "g"
  L42_2 = "h"
  L43_2 = "i"
  L44_2 = "j"
  L45_2 = "k"
  L46_2 = "l"
  L47_2 = "m"
  L48_2 = "n"
  L49_2 = "o"
  L50_2 = "p"
  L51_2 = "q"
  L52_2 = "r"
  L53_2 = "s"
  L54_2 = "t"
  L55_2 = "u"
  L56_2 = "v"
  L57_2 = "w"
  L58_2 = "x"
  L8_2[1] = L9_2
  L8_2[2] = L10_2
  L8_2[3] = L11_2
  L8_2[4] = L12_2
  L8_2[5] = L13_2
  L8_2[6] = L14_2
  L8_2[7] = L15_2
  L8_2[8] = L16_2
  L8_2[9] = L17_2
  L8_2[10] = L18_2
  L8_2[11] = L19_2
  L8_2[12] = L20_2
  L8_2[13] = L21_2
  L8_2[14] = L22_2
  L8_2[15] = L23_2
  L8_2[16] = L24_2
  L8_2[17] = L25_2
  L8_2[18] = L26_2
  L8_2[19] = L27_2
  L8_2[20] = L28_2
  L8_2[21] = L29_2
  L8_2[22] = L30_2
  L8_2[23] = L31_2
  L8_2[24] = L32_2
  L8_2[25] = L33_2
  L8_2[26] = L34_2
  L8_2[27] = L35_2
  L8_2[28] = L36_2
  L8_2[29] = L37_2
  L8_2[30] = L38_2
  L8_2[31] = L39_2
  L8_2[32] = L40_2
  L8_2[33] = L41_2
  L8_2[34] = L42_2
  L8_2[35] = L43_2
  L8_2[36] = L44_2
  L8_2[37] = L45_2
  L8_2[38] = L46_2
  L8_2[39] = L47_2
  L8_2[40] = L48_2
  L8_2[41] = L49_2
  L8_2[42] = L50_2
  L8_2[43] = L51_2
  L8_2[44] = L52_2
  L8_2[45] = L53_2
  L8_2[46] = L54_2
  L8_2[47] = L55_2
  L8_2[48] = L56_2
  L8_2[49] = L57_2
  L8_2[50] = L58_2
  L9_2 = "y"
  L10_2 = "z"
  L11_2 = "0"
  L12_2 = "1"
  L13_2 = "2"
  L14_2 = "3"
  L15_2 = "4"
  L16_2 = "5"
  L17_2 = "6"
  L18_2 = "7"
  L19_2 = "8"
  L20_2 = "9"
  L21_2 = "+"
  L22_2 = "/"
  L8_2[51] = L9_2
  L8_2[52] = L10_2
  L8_2[53] = L11_2
  L8_2[54] = L12_2
  L8_2[55] = L13_2
  L8_2[56] = L14_2
  L8_2[57] = L15_2
  L8_2[58] = L16_2
  L8_2[59] = L17_2
  L8_2[60] = L18_2
  L8_2[61] = L19_2
  L8_2[62] = L20_2
  L8_2[63] = L21_2
  L8_2[64] = L22_2
  cTable = L8_2
  L8_2 = {}
  L9_2 = nil
  L10_2 = nil
  L11_2 = nil
  L12_2 = nil
  L13_2 = nil
  L14_2 = nil
  L15_2 = nil
  L16_2 = math
  L16_2 = L16_2.floor
  L18_2 = L3_2
  L17_2 = L3_2.len
  L17_2 = L17_2(L18_2)
  L17_2 = L17_2 / 3
  L16_2 = L16_2(L17_2)
  L16_2 = L16_2 * 3
  L17_2 = 0
  L18_2 = 1
  L19_2 = L16_2
  L20_2 = 3
  for L21_2 = L18_2, L19_2, L20_2 do
    L17_2 = L17_2 + 1
    L23_2 = L3_2
    L22_2 = L3_2.byte
    L24_2 = L21_2
    L22_2 = L22_2(L23_2, L24_2)
    L9_2 = L22_2
    L23_2 = L3_2
    L22_2 = L3_2.byte
    L24_2 = L21_2 + 1
    L22_2 = L22_2(L23_2, L24_2)
    L10_2 = L22_2
    L23_2 = L3_2
    L22_2 = L3_2.byte
    L24_2 = L21_2 + 2
    L22_2 = L22_2(L23_2, L24_2)
    L11_2 = L22_2
    L22_2 = L7_2
    L23_2 = L9_2
    L24_2 = 2
    L22_2 = L22_2(L23_2, L24_2)
    L12_2 = L22_2
    L22_2 = L5_2
    L23_2 = L4_2
    L24_2 = L6_2
    L25_2 = L9_2
    L26_2 = 4
    L24_2 = L24_2(L25_2, L26_2)
    L25_2 = L7_2
    L26_2 = L10_2
    L27_2 = 4
    L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2, L54_2, L55_2, L56_2, L57_2, L58_2, L59_2 = L25_2(L26_2, L27_2)
    L23_2 = L23_2(L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2, L54_2, L55_2, L56_2, L57_2, L58_2, L59_2)
    L24_2 = 63
    L22_2 = L22_2(L23_2, L24_2)
    L13_2 = L22_2
    L22_2 = L5_2
    L23_2 = L4_2
    L24_2 = L6_2
    L25_2 = L10_2
    L26_2 = 2
    L24_2 = L24_2(L25_2, L26_2)
    L25_2 = L7_2
    L26_2 = L11_2
    L27_2 = 6
    L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2, L54_2, L55_2, L56_2, L57_2, L58_2, L59_2 = L25_2(L26_2, L27_2)
    L23_2 = L23_2(L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2, L54_2, L55_2, L56_2, L57_2, L58_2, L59_2)
    L24_2 = 63
    L22_2 = L22_2(L23_2, L24_2)
    L14_2 = L22_2
    L22_2 = L5_2
    L23_2 = L11_2
    L24_2 = 63
    L22_2 = L22_2(L23_2, L24_2)
    L15_2 = L22_2
    L22_2 = cTable
    L23_2 = L12_2 + 1
    L22_2 = L22_2[L23_2]
    L23_2 = cTable
    L24_2 = L13_2 + 1
    L23_2 = L23_2[L24_2]
    L24_2 = cTable
    L25_2 = L14_2 + 1
    L24_2 = L24_2[L25_2]
    L25_2 = cTable
    L26_2 = L15_2 + 1
    L25_2 = L25_2[L26_2]
    L22_2 = L22_2 .. L23_2 .. L24_2 .. L25_2
    L8_2[L17_2] = L22_2
  end
  L17_2 = L17_2 + 1
  L19_2 = L3_2
  L18_2 = L3_2.len
  L18_2 = L18_2(L19_2)
  L18_2 = L18_2 % 3
  if L18_2 == 1 then
    L19_2 = L3_2
    L18_2 = L3_2.byte
    L20_2 = L16_2 + 1
    L18_2 = L18_2(L19_2, L20_2)
    L9_2 = L18_2
    L18_2 = L7_2
    L19_2 = L5_2
    L20_2 = L9_2
    L21_2 = 252
    L19_2 = L19_2(L20_2, L21_2)
    L20_2 = 2
    L18_2 = L18_2(L19_2, L20_2)
    L12_2 = L18_2
    L18_2 = L6_2
    L19_2 = L5_2
    L20_2 = L9_2
    L21_2 = 3
    L19_2 = L19_2(L20_2, L21_2)
    L20_2 = 4
    L18_2 = L18_2(L19_2, L20_2)
    L13_2 = L18_2
    L18_2 = cTable
    L19_2 = L12_2 + 1
    L18_2 = L18_2[L19_2]
    L19_2 = cTable
    L20_2 = L13_2 + 1
    L19_2 = L19_2[L20_2]
    L20_2 = "=="
    L18_2 = L18_2 .. L19_2 .. L20_2
    L8_2[L17_2] = L18_2
  else
    L19_2 = L3_2
    L18_2 = L3_2.len
    L18_2 = L18_2(L19_2)
    L18_2 = L18_2 % 3
    if L18_2 == 2 then
      L19_2 = L3_2
      L18_2 = L3_2.byte
      L20_2 = L16_2 + 1
      L18_2 = L18_2(L19_2, L20_2)
      L9_2 = L18_2
      L19_2 = L3_2
      L18_2 = L3_2.byte
      L20_2 = L16_2 + 2
      L18_2 = L18_2(L19_2, L20_2)
      L10_2 = L18_2
      L18_2 = L7_2
      L19_2 = L5_2
      L20_2 = L9_2
      L21_2 = 252
      L19_2 = L19_2(L20_2, L21_2)
      L20_2 = 2
      L18_2 = L18_2(L19_2, L20_2)
      L12_2 = L18_2
      L18_2 = L4_2
      L19_2 = L6_2
      L20_2 = L5_2
      L21_2 = L9_2
      L22_2 = 3
      L20_2 = L20_2(L21_2, L22_2)
      L21_2 = 4
      L19_2 = L19_2(L20_2, L21_2)
      L20_2 = L7_2
      L21_2 = L5_2
      L22_2 = L10_2
      L23_2 = 240
      L21_2 = L21_2(L22_2, L23_2)
      L22_2 = 4
      L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2, L54_2, L55_2, L56_2, L57_2, L58_2, L59_2 = L20_2(L21_2, L22_2)
      L18_2 = L18_2(L19_2, L20_2, L21_2, L22_2, L23_2, L24_2, L25_2, L26_2, L27_2, L28_2, L29_2, L30_2, L31_2, L32_2, L33_2, L34_2, L35_2, L36_2, L37_2, L38_2, L39_2, L40_2, L41_2, L42_2, L43_2, L44_2, L45_2, L46_2, L47_2, L48_2, L49_2, L50_2, L51_2, L52_2, L53_2, L54_2, L55_2, L56_2, L57_2, L58_2, L59_2)
      L13_2 = L18_2
      L18_2 = L6_2
      L19_2 = L5_2
      L20_2 = L10_2
      L21_2 = 15
      L19_2 = L19_2(L20_2, L21_2)
      L20_2 = 2
      L18_2 = L18_2(L19_2, L20_2)
      L14_2 = L18_2
      L18_2 = cTable
      L19_2 = L12_2 + 1
      L18_2 = L18_2[L19_2]
      L19_2 = cTable
      L20_2 = L13_2 + 1
      L19_2 = L19_2[L20_2]
      L20_2 = cTable
      L21_2 = L14_2 + 1
      L20_2 = L20_2[L21_2]
      L21_2 = "="
      L18_2 = L18_2 .. L19_2 .. L20_2 .. L21_2
      L8_2[L17_2] = L18_2
    end
  end
  L18_2 = table
  L18_2 = L18_2.concat
  L19_2 = L8_2
  return L18_2(L19_2)
end

_ReadBase64 = L1_1

function L1_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L1_2 = fwShowWnd
  L2_2 = "wid"
  L3_2 = 660
  L4_2 = 200
  L5_2 = 1375
  L6_2 = 905
  L7_2 = 1
  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2)
  L1_2 = fwShowButton
  L2_2 = "wid"
  L3_2 = "vid"
  L4_2 = ""
  L5_2 = ""
  L6_2 = ""
  L7_2 = A0_2
  L8_2 = ".png"
  L7_2 = L7_2 .. L8_2
  L8_2 = 15
  L9_2 = 0
  L10_2 = 0
  L11_2 = 600
  L12_2 = 600
  L1_2(L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2)
  L1_2 = nil
  L2_2 = os
  L2_2 = L2_2.time
  L2_2 = L2_2()
  while true do
    L3_2 = fwGetPressedButton
    L3_2 = L3_2()
    if L3_2 == "vid" then
      L4_2 = fwCloseWnd
      L5_2 = "wid"
      L4_2(L5_2)
      L4_2 = playAudio
      L5_2 = ""
      L4_2(L5_2)
      L4_2 = mSleep
      L5_2 = 2000
      L4_2(L5_2)
      L4_2 = true
      return L4_2
    end
    L4_2 = UI_msg
    if L4_2 == "震动提示" then
      if L1_2 ~= nil then
        L4_2 = os
        L4_2 = L4_2.time
        L4_2 = L4_2()
        L4_2 = L4_2 - L1_2
      end
      if 1 < L4_2 then
        L4_2 = vibrator
        L4_2()
        L4_2 = os
        L4_2 = L4_2.time
        L4_2 = L4_2()
        L1_2 = L4_2
      end
    else
      if L1_2 ~= nil then
        L4_2 = os
        L4_2 = L4_2.time
        L4_2 = L4_2()
        L4_2 = L4_2 - L1_2
        if not (30 < L4_2) then
          goto lbl_76
        end
      end
      L4_2 = playAudio
      L5_2 = "634.wav"
      L4_2(L5_2)
      L4_2 = os
      L4_2 = L4_2.time
      L4_2 = L4_2()
      L1_2 = L4_2
    end
    ::lbl_76::
    L4_2 = os
    L4_2 = L4_2.time
    L4_2 = L4_2()
    L4_2 = L4_2 - L2_2
    if 300 < L4_2 then
      if A0_2 == "dd_zw" then
        L4_2 = fwCloseWnd
        L5_2 = "wid"
        L4_2(L5_2)
        L4_2 = playAudio
        L5_2 = ""
        L4_2(L5_2)
        L4_2 = mSleep
        L5_2 = 2000
        L4_2(L5_2)
        L4_2 = false
        return L4_2
      elseif A0_2 == "dd_7" then
        L4_2 = fwCloseWnd
        L5_2 = "wid"
        L4_2(L5_2)
        L4_2 = playAudio
        L5_2 = ""
        L4_2(L5_2)
        L4_2 = closeApp
        L5_2 = "com.netease.mhxyhtb"
        L4_2(L5_2)
        L4_2 = dialog
        L5_2 = "旗子处理提示超过5分钟,自动关闭游戏！"
        L6_2 = time
        L4_2(L5_2, L6_2)
        L4_2 = lua_exit
        L4_2()
        L4_2 = mSleep
        L5_2 = 10
        L4_2(L5_2)
      elseif A0_2 == "dd_wl" then
        L4_2 = fwCloseWnd
        L5_2 = "wid"
        L4_2(L5_2)
        L4_2 = playAudio
        L5_2 = ""
        L4_2(L5_2)
        L4_2 = closeApp
        L5_2 = "com.netease.mhxyhtb"
        L4_2(L5_2)
        L4_2 = dialog
        L5_2 = "网络正常,无法通讯服务器,提示超过5分钟,自动关闭游戏！"
        L6_2 = time
        L4_2(L5_2, L6_2)
        L4_2 = lua_exit
        L4_2()
        L4_2 = mSleep
        L5_2 = 10
        L4_2(L5_2)
      end
    end
    L4_2 = mSleep
    L5_2 = 200
    L4_2(L5_2)
  end
end

_call_script = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2)
  local L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  L7_2 = findImageInRegionFuzzy
  L8_2 = A0_2
  L9_2 = A6_2
  L10_2 = A1_2
  L11_2 = A2_2
  L12_2 = A3_2
  L13_2 = A4_2
  L14_2 = 0
  L7_2, L8_2 = L7_2(L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
  if -1 < L7_2 then
    if A5_2 then
      L9_2 = _tap
      L10_2 = L7_2
      L11_2 = L8_2
      L9_2(L10_2, L11_2)
    end
    L9_2 = _print
    L10_2 = A0_2
    L9_2(L10_2)
    L9_2 = true
    L10_2 = L7_2
    L11_2 = L8_2
    return L9_2, L10_2, L11_2
  else
    L9_2 = false
    return L9_2
  end
end

_findImg = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2, L16_2
  L2_2 = nil
  L3_2 = nil
  L4_2 = pairs
  L5_2 = A0_2
  L4_2, L5_2, L6_2 = L4_2(L5_2)
  for L7_2, L8_2 in L4_2, L5_2, L6_2 do
    L9_2 = findMultiColorInRegionFuzzyByTable
    L10_2 = L8_2[2]
    L11_2 = 85
    L12_2 = A1_2[1]
    L13_2 = A1_2[2]
    L14_2 = A1_2[3]
    L15_2 = A1_2[4]
    L9_2, L10_2 = L9_2(L10_2, L11_2, L12_2, L13_2, L14_2, L15_2)
    L3_2 = L10_2
    L2_2 = L9_2
    if -1 < L2_2 then
      L9_2 = _tap
      L10_2 = L2_2
      L11_2 = L3_2
      L9_2(L10_2, L11_2)
      L9_2 = _print
      L10_2 = L8_2[1]
      L9_2(L10_2)
      L9_2 = true
      return L9_2
    end
  end
  L4_2 = false
  return L4_2
end

p_find_tb = L1_1

function L1_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2
  L1_2 = io
  L1_2 = L1_2.open
  L2_2 = A0_2
  L3_2 = "rb"
  L1_2 = L1_2(L2_2, L3_2)
  L3_2 = L1_2
  L2_2 = L1_2.read
  L4_2 = "*all"
  L2_2 = L2_2(L3_2, L4_2)
  L4_2 = L1_2
  L3_2 = L1_2.close
  L3_2(L4_2)
  return L2_2
end

ReadFileByte = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  L2_2 = {}
  L3_2 = ipairs
  L4_2 = A0_2
  L3_2, L4_2, L5_2 = L3_2(L4_2)
  for L6_2, L7_2 in L3_2, L4_2, L5_2 do
    L8_2 = #L2_2
    L8_2 = L8_2 + 1
    L2_2[L8_2] = L7_2
  end
  L3_2 = pairs
  L4_2 = A1_2
  L3_2, L4_2, L5_2 = L3_2(L4_2)
  for L6_2, L7_2 in L3_2, L4_2, L5_2 do
    L8_2 = #L2_2
    L8_2 = L8_2 + 1
    L2_2[L8_2] = L7_2
  end
  return L2_2
end

_ENV["_合并table"] = L1_1

function L1_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  L1_2 = 1
  L2_2 = #A0_2
  L2_2 = L2_2 - 1
  L3_2 = 1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = L4_2 + 1
    L6_2 = #A0_2
    L7_2 = 1
    for L8_2 = L5_2, L6_2, L7_2 do
      L9_2 = A0_2[L4_2]
      L9_2 = L9_2.game_x
      L10_2 = A0_2[L8_2]
      L10_2 = L10_2.game_x
      if L9_2 == L10_2 then
        L9_2 = A0_2[L4_2]
        L9_2 = L9_2.game_y
        L10_2 = A0_2[L8_2]
        L10_2 = L10_2.game_y
        if L9_2 == L10_2 then
          L9_2 = A0_2[L8_2]
          L9_2.game_x = -1
        end
      end
    end
  end
  L1_2 = #A0_2
  L2_2 = 1
  L3_2 = -1
  for L4_2 = L1_2, L2_2, L3_2 do
    L5_2 = A0_2[L4_2]
    L5_2 = L5_2.game_x
    if L5_2 == -1 then
      L5_2 = table
      L5_2 = L5_2.remove
      L6_2 = A0_2
      L7_2 = L4_2
      L5_2(L6_2, L7_2)
    end
  end
end

_ENV["_table坐标去重"] = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2
  L2_2 = {}
  L3_2 = ipairs
  L4_2 = A0_2
  L3_2, L4_2, L5_2 = L3_2(L4_2)
  for L6_2, L7_2 in L3_2, L4_2, L5_2 do
    L8_2 = #L2_2
    L8_2 = L8_2 + 1
    L2_2[L8_2] = L7_2
  end
  L3_2 = pairs
  L4_2 = A1_2
  L3_2, L4_2, L5_2 = L3_2(L4_2)
  for L6_2, L7_2 in L3_2, L4_2, L5_2 do
    L8_2 = #L2_2
    L8_2 = L8_2 + 1
    L2_2[L8_2] = L7_2
  end
  L3_2 = #L2_2
  L4_2 = 1
  L5_2 = L3_2 - 1
  L6_2 = 1
  for L7_2 = L4_2, L5_2, L6_2 do
    L8_2 = L7_2 + 1
    L9_2 = L3_2
    L10_2 = 1
    for L11_2 = L8_2, L9_2, L10_2 do
      L12_2 = L2_2[L7_2]
      L12_2 = L12_2.y
      L13_2 = L2_2[L11_2]
      L13_2 = L13_2.y
      L12_2 = L12_2 - L13_2
      if L12_2 < 5 and -5 < L12_2 then
        L13_2 = L2_2[L11_2]
        L13_2.y = -1
      end
    end
  end
  L4_2 = #L2_2
  L5_2 = 1
  L6_2 = -1
  for L7_2 = L4_2, L5_2, L6_2 do
    L8_2 = L2_2[L7_2]
    L8_2 = L8_2.y
    if L8_2 == -1 then
      L8_2 = table
      L8_2 = L8_2.remove
      L9_2 = L2_2
      L10_2 = L7_2
      L8_2(L9_2, L10_2)
    end
  end
  return L2_2
end

_ENV["_table合并去重"] = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2, L12_2, L13_2
  L2_2 = {}
  L3_2 = ipairs
  L4_2 = A0_2
  L3_2, L4_2, L5_2 = L3_2(L4_2)
  for L6_2, L7_2 in L3_2, L4_2, L5_2 do
    L8_2 = #L2_2
    L8_2 = L8_2 + 1
    L2_2[L8_2] = L7_2
  end
  L3_2 = pairs
  L4_2 = A1_2
  L3_2, L4_2, L5_2 = L3_2(L4_2)
  for L6_2, L7_2 in L3_2, L4_2, L5_2 do
    L8_2 = #L2_2
    L8_2 = L8_2 + 1
    L2_2[L8_2] = L7_2
  end
  L3_2 = 1
  L4_2 = #L2_2
  L4_2 = L4_2 - 1
  L5_2 = 1
  for L6_2 = L3_2, L4_2, L5_2 do
    L7_2 = L6_2 + 1
    L8_2 = #L2_2
    L9_2 = 1
    for L10_2 = L7_2, L8_2, L9_2 do
      L11_2 = L2_2[L6_2]
      L11_2 = L11_2.game_x
      L12_2 = L2_2[L10_2]
      L12_2 = L12_2.game_x
      if L11_2 == L12_2 then
        L11_2 = L2_2[L6_2]
        L11_2 = L11_2.game_y
        L12_2 = L2_2[L10_2]
        L12_2 = L12_2.game_y
        if L11_2 == L12_2 then
          L11_2 = L2_2[L10_2]
          L11_2.game_y = -1
        end
      end
    end
  end
  L3_2 = #L2_2
  L4_2 = 1
  L5_2 = -1
  for L6_2 = L3_2, L4_2, L5_2 do
    L7_2 = L2_2[L6_2]
    L7_2 = L7_2.game_y
    if L7_2 == -1 then
      L7_2 = table
      L7_2 = L7_2.remove
      L8_2 = L2_2
      L9_2 = L6_2
      L7_2(L8_2, L9_2)
    end
  end
  return L2_2
end

_ENV["_table合并去重g"] = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2
  L2_2 = mSleep
  L3_2 = math
  L3_2 = L3_2.random
  L4_2 = A0_2
  L5_2 = A1_2
  L3_2, L4_2, L5_2, L6_2 = L3_2(L4_2, L5_2)
  L2_2(L3_2, L4_2, L5_2, L6_2)
end

My_msleep = L1_1

function L1_1(A0_2)
  local L1_2, L2_2, L3_2, L4_2
  L1_2 = nLog_debug
  if L1_2 then
    L1_2 = nLog
    L2_2 = A0_2
    L1_2(L2_2)
  end
  L1_2 = toast_debug
  if L1_2 then
    L1_2 = toast
    L2_2 = A0_2
    L3_2 = 1
    L1_2(L2_2, L3_2)
  end
end

printf = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2, A4_2, A5_2, A6_2, A7_2)
  local L8_2, L9_2, L10_2, L11_2, L12_2, L13_2, L14_2, L15_2
  if not A7_2 then
    A7_2 = 90
  end
  if not A5_2 then
    A5_2 = 5
  end
  L8_2 = findMultiColorInRegionFuzzyByTable
  L9_2 = A0_2[2]
  L10_2 = A7_2
  L11_2 = A1_2
  L12_2 = A2_2
  L13_2 = A3_2
  L14_2 = A4_2
  L8_2, L9_2 = L8_2(L9_2, L10_2, L11_2, L12_2, L13_2, L14_2)
  if L8_2 ~= -1 then
    if A6_2 == nil or A6_2 == true then
    end
    L10_2 = A0_2[1]
    if L10_2 ~= "" then
      L10_2 = printf
      L11_2 = A0_2[1]
      L10_2(L11_2)
    end
    L10_2 = true
    return L10_2
  else
    L10_2 = false
    return L10_2
  end
end

FC_Clicke = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2
  L2_2 = math
  L2_2 = L2_2.random
  L3_2 = A0_2
  L4_2 = A1_2
  return L2_2(L3_2, L4_2)
end

Rnumber = L1_1

function L1_1(A0_2, A1_2, A2_2)
  local L3_2, L4_2, L5_2, L6_2, L7_2
  if A2_2 == nil then
    A2_2 = 5
    L3_2 = Rnumber
    L4_2 = -5
    L5_2 = A2_2
    L3_2 = L3_2(L4_2, L5_2)
    A0_2 = L3_2 + A0_2
    L3_2 = Rnumber
    L4_2 = -5
    L5_2 = A2_2
    L3_2 = L3_2(L4_2, L5_2)
    A1_2 = L3_2 + A1_2
  else
    L3_2 = Rnumber
    L4_2 = A2_2 * 2
    L4_2 = A2_2 - L4_2
    L5_2 = A2_2
    L3_2 = L3_2(L4_2, L5_2)
    A0_2 = L3_2 + A0_2
    L3_2 = Rnumber
    L4_2 = A2_2 * 2
    L4_2 = A2_2 - L4_2
    L5_2 = A2_2
    L3_2 = L3_2(L4_2, L5_2)
    A1_2 = L3_2 + A1_2
  end
  L3_2 = touchDown
  L4_2 = 1
  L5_2 = A0_2
  L6_2 = A1_2
  L3_2(L4_2, L5_2, L6_2)
  L3_2 = mSleep
  L4_2 = Rnumber
  L5_2 = 30
  L6_2 = 200
  L4_2, L5_2, L6_2, L7_2 = L4_2(L5_2, L6_2)
  L3_2(L4_2, L5_2, L6_2, L7_2)
  L3_2 = touchUp
  L4_2 = 1
  L5_2 = A0_2
  L6_2 = A1_2
  L3_2(L4_2, L5_2, L6_2)
end

Click = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  if not A3_2 then
    A3_2 = 90
  end
  if not A1_2 then
    A1_2 = 5
  end
  L4_2 = findMultiColorInRegionFuzzyByTable
  L5_2 = A0_2[2]
  L6_2 = A3_2
  L7_2 = A0_2[1]
  L7_2 = L7_2[2]
  L8_2 = A0_2[1]
  L8_2 = L8_2[3]
  L9_2 = A0_2[1]
  L9_2 = L9_2[4]
  L10_2 = A0_2[1]
  L10_2 = L10_2[5]
  L4_2, L5_2 = L4_2(L5_2, L6_2, L7_2, L8_2, L9_2, L10_2)
  if L4_2 ~= -1 then
    if A2_2 == nil or A2_2 == true then
      L6_2 = Click
      L7_2 = L4_2
      L8_2 = L5_2
      L9_2 = A1_2
      L6_2(L7_2, L8_2, L9_2)
    end
    L6_2 = true
    return L6_2
  else
    L6_2 = false
    return L6_2
  end
end

FC_Click = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2, L8_2, L9_2
  if not A1_2 then
    A1_2 = 90
  end
  L2_2 = findMultiColorInRegionFuzzyByTable
  L3_2 = A0_2[2]
  L4_2 = A1_2
  L5_2 = A0_2[1]
  L5_2 = L5_2[2]
  L6_2 = A0_2[1]
  L6_2 = L6_2[3]
  L7_2 = A0_2[1]
  L7_2 = L7_2[4]
  L8_2 = A0_2[1]
  L8_2 = L8_2[5]
  L2_2, L3_2 = L2_2(L3_2, L4_2, L5_2, L6_2, L7_2, L8_2)
  if L2_2 ~= -1 then
    L4_2 = printf
    L5_2 = A0_2[1]
    L5_2 = L5_2[1]
    L4_2(L5_2)
  end
  L4_2 = L2_2
  L5_2 = L3_2
  return L4_2, L5_2
end

FC = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L2_2 = math
  L2_2 = L2_2.random
  L3_2 = -1
  L4_2 = 1
  L2_2 = L2_2(L3_2, L4_2)
  L3_2 = touchDown
  L4_2 = 1
  L5_2 = A0_2 + L2_2
  L6_2 = A1_2
  L3_2(L4_2, L5_2, L6_2)
  L3_2 = mSleep
  L4_2 = math
  L4_2 = L4_2.random
  L5_2 = 100
  L6_2 = 500
  L4_2, L5_2, L6_2, L7_2 = L4_2(L5_2, L6_2)
  L3_2(L4_2, L5_2, L6_2, L7_2)
  L3_2 = touchUp
  L4_2 = 1
  L5_2 = A0_2 + L2_2
  L6_2 = A1_2
  L3_2(L4_2, L5_2, L6_2)
  L3_2 = os
  L3_2 = L3_2.time
  L3_2 = L3_2()
  _ENV["卡点时间"] = L3_2
end

d2 = L1_1

function L1_1(A0_2, A1_2)
  local L2_2, L3_2, L4_2, L5_2, L6_2, L7_2
  L2_2 = math
  L2_2 = L2_2.random
  L3_2 = -1
  L4_2 = 1
  L2_2 = L2_2(L3_2, L4_2)
  L3_2 = touchDown
  L4_2 = 1
  L5_2 = A0_2 + L2_2
  L6_2 = A1_2 + L2_2
  L3_2(L4_2, L5_2, L6_2)
  L3_2 = mSleep
  L4_2 = math
  L4_2 = L4_2.random
  L5_2 = 10
  L6_2 = 20
  L4_2, L5_2, L6_2, L7_2 = L4_2(L5_2, L6_2)
  L3_2(L4_2, L5_2, L6_2, L7_2)
  L3_2 = touchUp
  L4_2 = 1
  L5_2 = A0_2 + L2_2
  L6_2 = A1_2 + L2_2
  L3_2(L4_2, L5_2, L6_2)
  L3_2 = os
  L3_2 = L3_2.time
  L3_2 = L3_2()
  _ENV["卡点时间"] = L3_2
end

d1 = L1_1

function L1_1(A0_2, A1_2, A2_2, A3_2)
  local L4_2, L5_2, L6_2, L7_2, L8_2, L9_2, L10_2, L11_2
  if not A2_2 then
    A2_2 = A0_2
  end
  if not A3_2 then
    A3_2 = A1_2
  end
  L4_2 = math
  L4_2 = L4_2.random
  L5_2 = A0_2
  L6_2 = A2_2
  L4_2 = L4_2(L5_2, L6_2)
  L5_2 = math
  L5_2 = L5_2.random
  L6_2 = A1_2
  L7_2 = A3_2
  L5_2 = L5_2(L6_2, L7_2)
  L6_2 = math
  L6_2 = L6_2.random
  L7_2 = -1
  L8_2 = 1
  L6_2 = L6_2(L7_2, L8_2)
  L7_2 = mSleep
  L8_2 = math
  L8_2 = L8_2.random
  L9_2 = 10
  L10_2 = 50
  L8_2, L9_2, L10_2, L11_2 = L8_2(L9_2, L10_2)
  L7_2(L8_2, L9_2, L10_2, L11_2)
  L7_2 = touchDown
  L8_2 = 1
  L9_2 = L4_2 + L6_2
  L10_2 = L5_2 + L6_2
  L7_2(L8_2, L9_2, L10_2)
  L7_2 = mSleep
  L8_2 = math
  L8_2 = L8_2.random
  L9_2 = 10
  L10_2 = 50
  L8_2, L9_2, L10_2, L11_2 = L8_2(L9_2, L10_2)
  L7_2(L8_2, L9_2, L10_2, L11_2)
  L7_2 = touchUp
  L8_2 = 1
  L9_2 = L4_2 + L6_2
  L10_2 = L5_2 + L6_2
  L7_2(L8_2, L9_2, L10_2)
  L7_2 = os
  L7_2 = L7_2.time
  L7_2 = L7_2()
  _ENV["卡点时间"] = L7_2
end

d = L1_1
